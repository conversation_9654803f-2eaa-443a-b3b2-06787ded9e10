<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="warn" packages="com.amazonaws.services.lambda.runtime.log4j2.LambdaAppender">

    <!-- Logging Properties -->
    <Properties>
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} ${ctx:messageId} %5p,%c{1}:%L,%m%n</Property>
    </Properties>


    <Appenders>
        <!-- Console Appender -->
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </Console>

        <Lambda name="Lambda">
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </Lambda>
    </Appenders>
    <Loggers>
        <Logger name="com.purolator" additivity="false" level="${env:LOG_LEVEL:-trace}">
            <AppenderRef ref="Console"/>
            <!--             <AppenderRef ref="Lambda"/>-->
        </Logger>

        <Root level="warn">
            <AppenderRef ref="Console"/>
            <!--             <AppenderRef ref="Lambda"/> -->
        </Root>
    </Loggers>

</Configuration>