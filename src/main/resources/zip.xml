<?xml version="1.0" encoding="UTF-8"?>
<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="
    http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2
      http://maven.apache.org/xsd/assembly-1.1.2.xsd">
    <includeBaseDirectory>false</includeBaseDirectory>
    <id>AddressTriageWorker</id>
    <formats>
        <format>zip</format>
    </formats>
    <fileSets>
        <fileSet>
            <outputDirectory>/</outputDirectory>
            <directory>/target</directory>
            <includes>
                <include>%regex[AutomatchWorker-.*[^shaded]\.jar]</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>