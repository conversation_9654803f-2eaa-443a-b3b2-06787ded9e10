package com.purolator.smartsort.automatch.util;

import com.purolator.smartsort.automatch.model.SmartSortApiResponse;
import com.purolator.smartsort.common.SmartSortException;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;

import java.io.IOException;
import java.net.ConnectException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

public class ExternalApiCall {

    // one instance, reuse
    private static final HttpClient httpClient = HttpClient.newBuilder()
            .version(HttpClient.Version.HTTP_2)
            .build();
    public static SmartSortApiResponse sendHttpApiCallWithBearer(String message, String bearerToken, String apiUrl, String method) throws SmartSortException {

       SmartSortApiResponse smartSortApiResponse = new SmartSortApiResponse();

        try {

            HttpRequest request = null;


            request = HttpRequest.newBuilder()
                    .method(method,HttpRequest.BodyPublishers.ofString(message))
                    .uri(URI.create(apiUrl))
                    .header(HttpHeaders.CONTENT_TYPE, "application/json") // add request header
                    .header(HttpHeaders.ACCEPT, "application/json") // add request header
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + bearerToken)
                    .build();

/*
            if (method.equalsIgnoreCase("POST")) {
                request = HttpRequest.newBuilder()
                        .POST(HttpRequest.BodyPublishers.ofString(message))
                        .uri(URI.create(apiUrl))
                        .header(HttpHeaders.CONTENT_TYPE, "application/json") // add request header
                        .header(HttpHeaders.ACCEPT, "application/json") // add request header
                        .header(HttpHeaders.AUTHORIZATION, "Bearer " + bearerToken)
                        .build();
            }
            else{
                request = HttpRequest.newBuilder()
                        .PUT(HttpRequest.BodyPublishers.ofString(message))
                        .uri(URI.create(apiUrl))
                        .header(HttpHeaders.CONTENT_TYPE, "application/json") // add request header
                        .header(HttpHeaders.ACCEPT, "application/json") // add request header
                        .header(HttpHeaders.AUTHORIZATION, "Bearer " + bearerToken)
                        .build();
            }
*/


            // use the client to send the request
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            smartSortApiResponse.setApiMessage(response.body());
            smartSortApiResponse.setResponseCode(response.statusCode());
            if(response.statusCode() == HttpStatus.SC_CREATED || response.statusCode() == HttpStatus.SC_NO_CONTENT)
            {
                smartSortApiResponse.setStatus("success");
            }
            else
            {
                smartSortApiResponse.setStatus("error");
            }
        }
        catch(IllegalArgumentException e){
            throw new SmartSortException(e.getMessage(), e, false);
        }
        catch(IllegalStateException e){
            throw new SmartSortException(e.getMessage(), e, false);
        }
        catch(ConnectException e){
           throw new SmartSortException(e.getMessage(), e, true);
        }
        catch (InterruptedException e) {
            throw new SmartSortException(e.getMessage(), e, true);
        }
        catch (IOException e) {
            throw new SmartSortException(e.getMessage(), e, true);
        }

        return smartSortApiResponse;
    }

}
