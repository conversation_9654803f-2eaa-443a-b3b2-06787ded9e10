package com.purolator.smartsort.automatch.util;

import com.purolator.smartsort.contract.SIAPIRequest;
import com.purolator.smartsort.contract.SIAPIResponse;

import java.util.Random;

/**
 * Mock service for handling SIAPI requests.
 * This service simulates the behavior of an actual API call by generating mock responses.
 *
 * Note: This is a placeholder implementation and should be replaced with the actual service logic.
 */
public class SiApiCall {

    private static final String[] ROUTE_VALUES = {"10B", "12C", "08G", "15A", "20D"};
    private static final String[] SHELF_VALUES = {"01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "A", "B", "C", "D", "PR", "DG"};
    private static final Random RANDOM = new Random();

    /**
     * <PERSON><PERSON> function to process SIAPIRequest and return SIAPIResponse.
     *
     * This method generates random values for the route, shelf, and delivery sequence ID
     * while keeping the piecePin consistent with the request.
     *
     * @param request the SIAPIRequest object containing input data
     * @return SIAPIResponse object with mock data
     */
    public static SIAPIResponse mockSiApiCall(SIAPIRequest request) {
        // Create a new response object

        // Validate the request and its fields
        if (request == null || request.getPiecePin() == null || request.getX() == null || request.getY() == null) {
            return null; // Return null if request, piecePin, x, or y is null
        }
        SIAPIResponse response = new SIAPIResponse();

        // Set the piecePin from the request
        response.setPiecePin(request.getPiecePin());

        // Generate random values for route and shelf
        response.setRoute(ROUTE_VALUES[RANDOM.nextInt(ROUTE_VALUES.length)]);
        response.setShelf(SHELF_VALUES[RANDOM.nextInt(SHELF_VALUES.length)]);

        // Generate a random delivery sequence ID
        response.setDeliverySequenceId(RANDOM.nextInt(1000) + 1); // Random ID between 1 and 1000

        // Return the mock response
        return response;
    }
}