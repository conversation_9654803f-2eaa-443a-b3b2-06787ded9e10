package com.purolator.smartsort.automatch.util;

import com.purolator.smartsort.worker.SmartSortWorkerException;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;

public class JsonSearch {

    public static String returnAttributeValue(JSONArray array, String searchValue) throws SmartSortWorkerException {
        String result = "";
        for (int i = 0; i < array.length(); i++) {
            JSONObject obj= null;
            try {
                obj = array.getJSONObject(i);
                if(obj.getString("name").equals(searchValue))
                {
                    result = obj.getString("value");
                    break;
                }
            } catch (JSONException e) {
                e.printStackTrace();
                throw new SmartSortWorkerException(e.getMessage(), false);
            }
        }
        return result;
    }

    public static ArrayList returnAttributeValueList(JSONArray array, String searchValue) throws SmartSortWorkerException{
        ArrayList<String> result = new ArrayList<String>();
        for (int i = 0; i < array.length(); i++) {
            JSONObject obj= null;
            try {
                obj = array.getJSONObject(i);
                if(obj.getString("name").equals(searchValue))
                {
                    result.add(obj.getString("value"));
                }
            } catch (JSONException e) {
                e.printStackTrace();
                throw new SmartSortWorkerException(e.getMessage(), false);
            }
        }
        return result;
    }
}
