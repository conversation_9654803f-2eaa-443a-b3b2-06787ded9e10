package com.purolator.smartsort.automatch.util;

import java.util.Arrays;
import java.util.List;

public final class AutomatchWorkerConstants {
    public static final String ERROR_PUBLISHING_PIN_ACTIVITY = "Error publishing PinActivity to SQS";
    public static final String ERROR_PUBLISHING_ADDRESS_TRIAGE = "Error publishing Address Triage to SQS";
    public static final String ERROR_PUSHING_PUBLISH_EVENT = "Error pushing Publish Event message to SQS";
    public static final String ERROR_RETRIEVING_SORT_CALENDAR = "Error retrieving sort calendar for terminal";
    public static final String ERROR_RELEASING_PIN_MASTER_LOCK = "Error releasing pin master lock";
    public static final String ERROR_ACQUIRING_PIN_MASTER_LOCK = "Error acquiring pin master lock";
    public static final String ERROR_PROCESSING_ROUTE_PLAN_LOOKUP = "Error processing Route Plan Lookup";
    public static final String ERROR_CHECKING_MAINTENANCE_WINDOW = "Error checking maintenance window for terminal";
    public static final String ERROR_RETRIEVING_SHELF_OVERRIDE = "Error retrieving shelf override";
    public static final String MESSAGE_SI_API_SUCCESS = "Processed SI API match";
    public static final String MESSAGE_GET_RP_FAILURE = "No match found in GetRouteInformation";
    public static final String MATCH_TYPE_NO_MATCH = "There is not any matched record in route plan from getRouteInformation SP";

    public static final String PIN_MASTER_STATE_CODE_FAM = "FAM";
    public static final String PIN_MASTER_STATE_CODE_RES = "RES";
    public static final String PIN_MASTER_STATE_CODE_ATQ = "ATQ";
    public static final String PIN_MASTER_STATE_CODE_RAM = "RAM";
    public static final String PIN_MASTER_STATE_CODE_RER = "RER";
    public static final String PIN_MASTER_STATE_CODE_PR = "PR";
    public static final String MESSAGE_SUCCESS = "S";
    public static final String MESSAGE_FAILURE = "F";
    public static final String PIN_MASTER_ROUTE_SRR = "SRR";
    public static final String EVENT_CODE_4000 = "4000";
    public static final String EVENT_CODE_4009 = "4009";
    public static final String TRIAGE_TYPE_A = "A";

    // Define the parameters for the query
    public static final String SUP_SYSTEM_PARAM_CONTEXT_SERVICE_TIME = "ServiceTime";
    public static final String SUP_SYSTEM_PARAMCONTEXT_DG_CLASS = "DangerousGoodsClass";
    public static final String SUP_SYSTEM_PARAM_NAME_TRUCK_SHELF_OVERRIDE = "TruckShelfOverride";
    public static final String SUP_SYSTEM_PARAM_CONTEXT_TERMINAL = "Terminal";
    public static final String SUP_SYSTEM_PARAM_NAME_WORKING_DATE = "WorkingDate";

    public static final List<String> PUROLATOR_AV_STATUS_UNPARSABLE_LIST = Arrays.asList("Unparsable", "AWSUnavailible");
    public static final List<String> PUROLATOR_AV_INFO_LIST = Arrays.asList("GeneralDelivery", "POBox", "RuralRoute");

    public static final String SOURCE_SYSTEM_ID = "SMARTSORT";
    public static final String SOURCE_SYSTEM_ID_VERSION = "1.0";

    // Private constructor to prevent instantiation
    private AutomatchWorkerConstants() {
    }


}
