package com.purolator.smartsort.automatch.context;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.purolator.smartsort.worker.SmartSortWorkerContext;

public class AutomatchWorkerContext extends SmartSortWorkerContext
{
	public AutomatchWorkerContext(ObjectMapper objectMapper) {
		super(objectMapper);
	}

	@Override
	public String getDatasourceSecretName() {
		return String.format("smartsort/%s/datasource", this.getEnvironment());
	}
}
