package com.purolator.smartsort.automatch.worker;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import com.purolator.smartsort.automatch.util.AutomatchWorkerConstants;
import com.purolator.smartsort.common.SmartSortException;
import com.purolator.smartsort.contract.*;
import com.purolator.smartsort.persistence.entity.*;
import com.purolator.smartsort.service.*;
import com.purolator.smartsort.service.impl.*;

import com.purolator.smartsort.util.DateUtil;
import com.purolator.smartsort.worker.LambdaProcessingResult;
import com.purolator.smartsort.worker.ProcessingObject;
import com.purolator.smartsort.worker.SmartSortLambdaWorker;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.ThreadContext;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.events.SQSEvent;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JSR310Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.purolator.smartsort.automatch.context.AutomatchWorkerContext;

import static com.purolator.smartsort.automatch.util.SiApiCall.mockSiApiCall;

public class AutomatchWorker extends SmartSortLambdaWorker<AutomatchMessage, AutomatchWorkerContext> {
    private static final Logger logger = LogManager.getLogger(AutomatchWorker.class);
   ObjectMapper objectMapper = JsonMapper.builder()
            .addModule(new JavaTimeModule())
            .build();
    private AutomatchWorkerContext workerContext = null;
    Map<String, SQSEvent.MessageAttribute> msgAttr = null;
    private static final String PATTERN_MESSAGE_DATE_TIME = "yyyy-MM-dd HH:mm:ss";

    private PinMasterSvc pinMasterSvc;
    private PinInfoSvc pinInfoSvc;
    private FacilityMasterSvc facilityMasterSvc;
    private SortPlanCalendarSvc sortPlanCalendarSvc;
    private SupSystemParametersSvc supSystemParametersSvc;
    private TriageQueueSvc triageQueueSvc;
    private TriageHistorySvc triageHistorySvc;
    private RouteInformationSvc routeInformationSvc;

    private final String LOCK_EXPIRATION_MINUTES_SECONDS = System.getenv("LOCK_EXPIRATION_IN_SECONDS");
    private final String PIN_ACTIVITY_QUEUE = System.getenv("PIN_ACTIVITY_QUEUE");
    private final String ADDRESS_TRIAGE_QUEUE = System.getenv("ADDRESS_TRIAGE_QUEUE_URL");
    private final String PUBLISH_EVENT_QUEUE = System.getenv("PUBLISH_EVENT_QUEUE_URL");
    private final String MAX_MAINTENANCE_DELAY = System.getenv("MAX_MAINTENANCE_DELAY");
    private int lockExpirationSeconds = 60;
    private int maxMaintanenceDelay = 900;
    private String sourceWorker;
    private String shelfOverride;
    private boolean isMaintenanceWindow = false;
    private long calculatedMaintenanceDelaySeconds = 0;


    public AutomatchWorker() {
        super(AutomatchMessage.class);
    }

    @Override
    protected void init(Context context) throws SmartSortException {
        super.init(context);
        objectMapper = JsonMapper.builder().findAndAddModules().build();
        objectMapper.registerModule(new JSR310Module());
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        workerContext = new AutomatchWorkerContext(objectMapper);
        if (LOCK_EXPIRATION_MINUTES_SECONDS != null) {
            lockExpirationSeconds = Integer.parseInt(LOCK_EXPIRATION_MINUTES_SECONDS);
        }
        if (MAX_MAINTENANCE_DELAY != null) {
            maxMaintanenceDelay = Integer.parseInt(MAX_MAINTENANCE_DELAY);
        }
        sourceWorker = getFunctionName();
    }

    @Override
    protected void handleMessages(AutomatchWorkerContext automatchWorkerContext,
                                  List<ProcessingObject<AutomatchMessage>> messages, String s) {

        for (ProcessingObject<AutomatchMessage> processMessage : messages) {
            ThreadContext.put("messageId", processMessage.getMsgId());

            String piecePin = null; // Declare piecePin for use in finally block
            // Deserialize the message from SQS
            AutomatchMessage automatchMessage = null;
            try {
                automatchMessage = objectMapper.readValue(processMessage.getSqsMsg().getBody(), AutomatchMessage.class);
            }
            catch (JsonMappingException e) {
                logger.error("JSON mapping Exception", e);
                processMessage.getStatus().setStatus(LambdaProcessingResult.Status.FAIL);
                return;
            } catch (JsonProcessingException e) {
                logger.error("JSON Processing Exception", e);
                processMessage.getStatus().setStatus(LambdaProcessingResult.Status.FAIL);
                return;
            } catch (Exception e) {
                logger.error("Error deserializing message: {}", e.getMessage());
                processMessage.getStatus().setStatus(LambdaProcessingResult.Status.FAIL);
                return;
            }

            // Check if the destination terminal is in a maintenance window
            try {
                checkMaintenanceWindow(automatchMessage.getDestinationTerminal());
                // If the terminal is in a maintenance window, requeue the message with a delay
                if (isMaintenanceWindow) {
                    logger.info("Message is in maintenance window. The length of window is {} seconds.", calculatedMaintenanceDelaySeconds);
                    processMessage.getStatus().setStatus(LambdaProcessingResult.Status.DELAY);
                    // If the calculated maintenance delay exceeds the maximum allowed, cap it
                    if (calculatedMaintenanceDelaySeconds > maxMaintanenceDelay) {
                        calculatedMaintenanceDelaySeconds = maxMaintanenceDelay;
                    }
                    // Set the maximum delay time for the message processing
                    logger.info("Message is in maintenance window. Requeuing message with delay of {} seconds.", calculatedMaintenanceDelaySeconds);
                    processMessage.setMaxDelayTime((int) calculatedMaintenanceDelaySeconds);
                    return;
                }
            }
            // Handle exceptions that occur while checking the maintenance window
            catch (SmartSortException e) {
                logger.error("Error checking maintenance window: {}", e.getMessage());
                processMessage.getStatus().setStatus(LambdaProcessingResult.Status.RETRY);
            }
            catch (Exception e) {
                logger.error("Error checking maintenance window: {}", e.getMessage());
                processMessage.getStatus().setStatus(LambdaProcessingResult.Status.RETRY);
            }

            try {
                try (Connection connection = getWorkerContext().getDataSource().getConnection()) {
                    //  START Transaction
                    connection.setAutoCommit(false);
                    piecePin = automatchMessage.getAutomatchModels().get(0).getPiecePin();
                    try {
                        //do processing
                        handleSingleMessage(automatchMessage,connection);
                        // END Transaction
                        connection.commit();
                        connection.setAutoCommit(true);
                    } catch (SmartSortException e) {
                        logger.error("Exception encountered when trying to process Automatch message.");
                        logger.info(e.getMessage());
                        if (e.getCause() != null) {
                            logger.error(e.getCause().getMessage());
                        }
                        if (e.isRecoverable()) {
                            processMessage.getStatus().setStatus(LambdaProcessingResult.Status.RETRY);
                        } else {
                            processMessage.getStatus().setStatus(LambdaProcessingResult.Status.FAIL);
                        }
                        connection.rollback();
                        connection.setAutoCommit(true);
                    }
                    catch (Exception e) {
                        logger.error("Exception encountered when trying to process Automatch. Roll back previous changes. Will retry.");

                        connection.rollback();
                        connection.setAutoCommit(true);
                        logger.info(e.getMessage());
                        if (e.getCause() != null) {
                            logger.error(e.getCause().getMessage());
                        }
                        processMessage.getStatus().setStatus(LambdaProcessingResult.Status.RETRY);
                    }
                } catch (SQLException e) {
                    logger.info("Error trying to acquire connection.");
                    logger.info("ERROR: {}", e.getMessage());
                    logger.info("{} - {}", e.getSQLState(), e.getErrorCode());
                    if (e.getCause() != null) {
                        logger.info("Caused By: {}", e.getCause().getMessage());
                    }
                    processMessage.getStatus().setStatus(LambdaProcessingResult.Status.RETRY);
                }
                finally {
                    // Release locks
                    if (piecePin != null) {
                        try (Connection connection = getWorkerContext().getDataSource().getConnection()) {
                            releasePinMasterLock(connection, piecePin); // Release the lock
                        } catch (SQLException e) {
                            logger.error( AutomatchWorkerConstants.ERROR_RELEASING_PIN_MASTER_LOCK + ": {}", e.getMessage(), e);
                        }
                    }
                }
            } catch (SmartSortException e) {
                logger.error("Exception encountered when trying to process Automatch message.");
                logger.info(e.getMessage());
                if (e.getCause() != null) {
                    logger.error(e.getCause().getMessage());
                }
                if (e.isRecoverable()) {
                    processMessage.getStatus().setStatus(LambdaProcessingResult.Status.RETRY);
                    return;
                } else {
                    processMessage.getStatus().setStatus(LambdaProcessingResult.Status.FAIL);
                    return;
                }
            }
            catch (Exception e) {
                logger.info("Error in automatch processing.");
                logger.info(e.getMessage());
                if (e.getCause() != null) {
                    logger.debug("Caused By: {}", e.getCause().getMessage());
                }
                processMessage.getStatus().setStatus(LambdaProcessingResult.Status.FAIL);
                return;
            }
        }
    }

    private void handleSingleMessage(AutomatchMessage message, Connection connection) throws SmartSortException {

        List<AutomatchModel> automatchModels = message.getAutomatchModels();
        AutomatchModel automatchModel = automatchModels.get(0);
        PinMaster pinMaster = null;
        PinInfo pinInfo = null;

        long startTime = System.nanoTime(); // Start time
        try{
            logger.info("Processing automatch message: {}", automatchModel.getPiecePin());

            try {
                logger.debug("Acquiring Pin Master lock for pin: {}",  automatchModel.getPiecePin());
                if(getPinMasterSvc().acquirePinMasterLock(automatchModel.getPiecePin(), lockExpirationSeconds, connection))
                {
                    logger.debug("Successfully acquired Pin Master lock for pin: {}",  automatchModel.getPiecePin());
                }
                else{
                    logger.debug( AutomatchWorkerConstants.ERROR_ACQUIRING_PIN_MASTER_LOCK + "for pin: {}. Will retry.",  automatchModel.getPiecePin());
                    throw new SmartSortException(AutomatchWorkerConstants.ERROR_ACQUIRING_PIN_MASTER_LOCK + ". Will retry.",  true);

                };
            } catch (SmartSortException e) {
                logger.info("Exception encountered when trying to acquire pin master lock.");
                logger.info(e.getMessage());
                if (e.getCause() != null) {
                    logger.error(e.getCause().getMessage());
                    throw new SmartSortException(e.getCause().getMessage(), e.isRecoverable());
                }
            } catch (Exception e) {
                logger.info("Error Trying to acquire acquire Pin Master lock.");
                logger.info(e.getMessage());
                if (e.getCause() != null) {
                    logger.debug("Caused By: {}", e.getCause().getMessage());
                }
                throw new RuntimeException();
            }

            // Check if pin_master_state_code is 'RAM' or 'PR'
            try {
                pinMaster = getPinMasterSvc().findPinMasterRecordByPiecePin(connection,automatchModel.getPiecePin());
                if (pinMaster == null ) {
                    logger.info(String.format("Pin master record not found for pin master id: %s. Ignoring message.", automatchModel.getPiecePin()));
                    return;
                }
                if (!Arrays.asList(AutomatchWorkerConstants.PIN_MASTER_STATE_CODE_RAM, AutomatchWorkerConstants.PIN_MASTER_STATE_CODE_PR).contains(pinMaster.getPinMasterStateCode())) {
                    logger.info(String.format("not a valid pin_master_state_code for autoMatch or PinReset. Ignoring message: %s", automatchModel.getPiecePin()));
                    logger.info("Release pin master lock.");
                    return;
                }
                logger.info(String.format("Pin master record found for pin master id: %s.", automatchModel.getPiecePin()));
            }
            catch (SmartSortException e) {
                logger.error("Exception encountered when trying to process Automatch message.");
                logger.info(e.getMessage());
                if (e.getCause() != null) {
                    logger.error(e.getCause().getMessage());
                }
                if (e.isRecoverable()) {
                    throw new SmartSortException(e.getMessage(), true);
                } else {
                    throw new SmartSortException(e.getMessage(), false);
                }
            }
            catch (Exception e) {
                logger.info("Error in automatch processing.");
                logger.info(e.getMessage());
                if (e.getCause() != null) {
                    logger.debug("Caused By: {}", e.getCause().getMessage());
                }
                throw new RuntimeException();
            }

            //Get PinInfo record
            try {
                pinInfo = getPinInfoSvc().findPinInfoRecordByPinMasterId(connection,automatchModel.getPinMasterId().toString());

                if (pinInfo == null ) {
                    logger.info(String.format("Pin info record not found for pin master id: %s. Ignoring message.", automatchModel.getPiecePin()));
                    return;
                }
                logger.info(String.format("Pin info record found for pin master id: %s.", automatchModel.getPiecePin()));
            }
            catch (SmartSortException e) {
                logger.error("Exception encountered when trying to process Automatch message.");
                logger.info(e.getMessage());
                if (e.getCause() != null) {
                    logger.error(e.getCause().getMessage());
                }
                if (e.isRecoverable()) {
                    throw new SmartSortException(e.getMessage(), true);
                } else {
                    throw new SmartSortException(e.getMessage(), false);
                }
            }
            catch (Exception e) {
                logger.info("Error in automatch processing.");
                logger.info(e.getMessage());
                if (e.getCause() != null) {
                    logger.debug("Caused By: {}", e.getCause().getMessage());
                }
                throw new RuntimeException();
            }

            FacilityMaster facilityMaster = getFacilityMasterSvc().getFacilitiesMap().get(message.getDestinationTerminal());
            if(facilityMaster.isSiApiEnable()) {
                // Call SI API
                logger.info(String.format("Terminal %s is api enabled. Will call API.", message.getDestinationTerminal()));
                handleSIAPISingleMessage(pinMaster, pinInfo, facilityMaster, connection, automatchModel);
                return;
            }
            else{ // Use Route plan lookup logic
                try {
                    logger.info("The Terminal is not Geocoding API enabled. Proceeding with Route Plan Lookup.");
                        try {
                            performRoutePlanLookup(pinMaster, pinInfo, connection, automatchModel);
                        } catch (SmartSortException e) {
                            logger.info("Exception encountered when trying to do route plan lookup.");
                            logger.info(e.getMessage());
                            if (e.getCause() != null) {
                                logger.error(e.getCause().getMessage());
                                throw new SmartSortException(e.getCause().getMessage(), e.isRecoverable());
                            }
                        }
                }catch (SmartSortException e) {
                    logger.info("Exception encountered when trying to do route plan lookup.");
                    logger.info(e.getMessage());
                    if (e.getCause() != null) {
                        logger.error(e.getCause().getMessage());
                        throw new SmartSortException(e.getCause().getMessage(), e.isRecoverable());
                    }
                }
                catch (Exception e) {
                    logger.info("Error in automatch processing.");
                    logger.info(e.getMessage());
                    if (e.getCause() != null) {
                        logger.debug("Caused By: {}", e.getCause().getMessage());
                    }
                    throw new RuntimeException();
                }
            }
        }
        finally {
            long endTime = System.nanoTime(); // End time
            long executionTimeMillis = (endTime - startTime) / 1_000_000; // Convert to milliseconds
            logger.info("Execution time for handleSingleMessage: {} ms", executionTimeMillis);
        }
    }

    private void releasePinMasterLock(Connection connection, String piecePin) {
        try {
            if (getPinMasterSvc().releasePinMasterLock(connection,piecePin)) {
                logger.debug("Successfully released Pin Master lock for pin: {}", piecePin);
            }
        } catch (Exception e) {
            logger.info(AutomatchWorkerConstants.ERROR_RELEASING_PIN_MASTER_LOCK + ". We are at the end of processing message. Will not throw retry.");
            logger.info(e.getMessage());
            if (e.getCause() != null) {
                logger.debug("Caused By: {}", e.getCause().getMessage());
            }
        }
    }

    private void handleSIAPISingleMessage(PinMaster pinMaster, PinInfo pinInfo, FacilityMaster facilityMaster, Connection connection, AutomatchModel automatchModel) throws SmartSortException {
        try {
//            // Check if terminal is SI API enabled
//            if (!facilityMaster.isSiApiEnable()) {
//                logger.info("SI API is not enabled for terminal: {}. Proceeding with Route Plan Lookup.", pinInfo.getDestinationTerminal());
//                performRoutePlanLookup(pinMaster, pinInfo, connection, automatchModel);
//                return;
//            }

            Integer confidenceThresholdValue = facilityMaster.getConfidenceThreshold();
            String confidenceThresholdStr = confidenceThresholdValue != null
                    ? confidenceThresholdValue.toString()
                    : "0";
            BigDecimal confidenceThreshold = new BigDecimal(confidenceThresholdStr);
            logger.info("Confidence threshold for terminal {}: {}", facilityMaster.getTerminal(), confidenceThreshold);

            BigDecimal confidenceThresholdValuePinInfo = pinInfo.getConfidenceLevel();
            String confidenceThresholdPinInfoStr = confidenceThresholdValuePinInfo != null
                    ? confidenceThresholdValuePinInfo.toString()
                    : "0";
            BigDecimal confidenceThresholdPinInfo = new BigDecimal(confidenceThresholdPinInfoStr);
            logger.info("Confidence level for PIN {}: {}", pinMaster.getPiecePin(), confidenceThresholdPinInfo);

            // Check if pinMasterStateCode is 'RAM', lat, long, confidence level
            //BigDecimal confidenceThreshold = new BigDecimal(facilityMaster.getConfidenceThreshold());
            if (pinMaster.getPinMasterStateCode().equals(AutomatchWorkerConstants.PIN_MASTER_STATE_CODE_RAM) &&
                    pinInfo.getLatitude() != null && pinInfo.getLongitude() != null &&
                    (confidenceThresholdPinInfo.compareTo(confidenceThreshold) >= 0)) {

                // Placeholder for SI API call
                  logger.info("Calling SI API for terminal: {}", pinInfo.getDestinationTerminal());
                //Call SI API and get response
                SIAPIResponse siapiResponse = getSiapiRespons(pinInfo);

                if (siapiResponse != null && siapiResponse.getRoute() != null && siapiResponse.getShelf() != null) {
                    // Process match found
                    logger.info("Match found from SI API for PIN: {}", pinMaster.getPiecePin());
                    processSIAPIMatch(pinMaster, pinInfo, siapiResponse, facilityMaster.getSiApiType(),automatchModel,connection);
                } else {
                    // No match found, proceed with Route Plan Lookup
                    logger.info("No match found from SI API for PIN: {}. Proceeding with Route Plan Lookup.", pinMaster.getPiecePin());
                    performRoutePlanLookup(pinMaster, pinInfo, connection, automatchModel);
                }
            } else {
                logger.info("Invalid latitude, longitude, or confidence level for PIN: {}. Proceeding with Route Plan Lookup.", pinMaster.getPiecePin());
                performRoutePlanLookup(pinMaster, pinInfo, connection, automatchModel);
            }
        } catch (Exception e) {
            logger.error("Error in handleSIAPISingleMessage: {}", e.getMessage());
            throw new SmartSortException(e.getMessage(), false);
        }
    }

    /**
     * Checks if the specified terminal is in a maintenance window and calculates the delay time.
     *
     * The function retrieves the FacilityMaster record for the given terminal and determines
     * whether the terminal is in a maintenance window based on its start and end times, as well
     * as its UTC offsets for standard and daylight time. It sets the `isMaintenanceWindow` flag
     * and calculates the `maintenanceDelaySeconds`.
     *
     * @param terminal The terminal identifier as a string.
     * @throws SmartSortException If an error occurs while retrieving the FacilityMaster or calculating the delay.
     */
    private void checkMaintenanceWindow(String terminal) throws SmartSortException {
        try {
            // Retrieve the FacilityMaster record for the given terminal
            FacilityMaster facilityMaster = getFacilityMasterSvc().getFacilitiesMap().get(terminal);

            // Calculate the maintenance delay time
            calculatedMaintenanceDelaySeconds = DateUtil.calculateMaintanenceDelayTime(
                    facilityMaster.getTermMaintStartTime(),
                    facilityMaster.getTermMaintEndTime(),
                    facilityMaster.getUtcOffsetStandard(),
                    facilityMaster.getUtcOffsetDaylight()
            );

            // Check if the terminal is in a maintenance window
            if (calculatedMaintenanceDelaySeconds == 0) {
                logger.info("Terminal is not in maintenance window. Proceeding with operations.");
                isMaintenanceWindow = false;
            } else {
                //logger.info("Terminal is in maintenance window. Requeuing message with delay of {} seconds.", calculatedMaintenanceDelaySeconds);
                isMaintenanceWindow = true;
            }
        } catch (Exception e) {
            // Log the error and throw a SmartSortException
            logger.error(AutomatchWorkerConstants.ERROR_CHECKING_MAINTENANCE_WINDOW + ": {}", terminal, e);
            throw new SmartSortException(AutomatchWorkerConstants.ERROR_CHECKING_MAINTENANCE_WINDOW + ": terminal", e, false);
        }
    }

    private SIAPIResponse getSiapiRespons(PinInfo pinInfo) {
        SIAPIRequest siapiRequest = new SIAPIRequest();
        siapiRequest.setPiecePin(pinInfo.getPiecePin());
        siapiRequest.setAddress(pinInfo.getDeclaredAddressLine1());
        siapiRequest.setCity(pinInfo.getDeclaredCity());
        siapiRequest.setPostal(pinInfo.getDeclaredPostalCode());
        siapiRequest.setRoutePlanName("routplan");
        siapiRequest.setX(pinInfo.getLatitude().toString());
        siapiRequest.setY(pinInfo.getLongitude().toString());

        SIAPIResponse siapiResponse = callSIAPI(siapiRequest);
        return siapiResponse;
    }

    private SIAPIResponse callSIAPI(SIAPIRequest siapiRequest) {
        // Placeholder for actual SI API call
        // Construct the request and call the API
        // Replace with actual API response
        return mockSiApiCall(siapiRequest);
    }

    private void processSIAPIMatch(PinMaster pinMaster, PinInfo pinInfo, SIAPIResponse siapiResponse, String siApiType, AutomatchModel automatchModel, Connection connection) throws SmartSortException {
        // Call GetRouteInformation SP

        RouteInformation routeInfo = getRouteInformationSvc().findRouteInformation(connection, pinInfo.getDestinationTerminal(),
                pinInfo.getCurrentPostalCode(), pinInfo.getCurrentStreetName(), pinInfo.getCurrentStreetType(),
                pinInfo.getCurrentStreetDirection(), pinInfo.getCurrentStreetNumber(), pinInfo.getCurrentStreetNumberSuffix(),
                pinMaster.getUnitNumber(), pinInfo.getCurrentCity(), pinMaster.getCustomerName(),
                pinInfo.getHoldForPickup(), pinMaster.getServiceTime(), pinInfo.getPurolatorAvStatus(), true);
        // Get shelf override
        shelfOverride = determineShelfOverride(pinMaster,routeInfo,connection);

        boolean isResolvedByApi = true;
        if (routeInfo != null) {
            // Update pin_master with daily_chg_route and daily_chg_shelf
            getPinMasterSvc().updatePinMasterWithPinInfo(connection, pinMaster.getPinMasterId(),pinInfo,
                     routeInfo, routeInfo.getRoute(), routeInfo.getShelf(), sourceWorker,AutomatchWorkerConstants.PIN_MASTER_STATE_CODE_RER,
                    shelfOverride,null);
            isResolvedByApi =false;
        } else {
            // Update pin_master with api_route and api_shelf
            getPinMasterSvc().updatePinMasterWithPinInfo(connection, pinMaster.getPinMasterId(),pinInfo,
                    routeInfo, siapiResponse.getRoute(), siapiResponse.getShelf(), sourceWorker,AutomatchWorkerConstants.PIN_MASTER_STATE_CODE_RER,
                    shelfOverride,siApiType);
        }

        //Get updated PinMaster record
        PinMaster pinMasterUpdated = getPinMasterSvc().findPinMasterRecordByPiecePin(connection, pinMaster.getPiecePin());
        // Push messages to SQS
        publishToPublishEventSQS(pinMasterUpdated, automatchModel, AutomatchWorkerConstants.EVENT_CODE_4000);
        publishToPinActivitySQS(pinMasterUpdated, pinInfo, automatchModel, AutomatchWorkerConstants.MESSAGE_SUCCESS, AutomatchWorkerConstants.MESSAGE_SI_API_SUCCESS,
                siapiResponse,routeInfo, isResolvedByApi,siApiType);
    }

    private void performRoutePlanLookup(PinMaster pinMaster, PinInfo pinInfo, Connection connection, AutomatchModel automatchModel) throws SmartSortException {
        // Implement route plan lookup logic

        try{
            // Check if the purolator_av_info is one of following GeneralDelivery, POBox, RuralRoute
            if (AutomatchWorkerConstants.PUROLATOR_AV_INFO_LIST.contains(pinInfo.getPurolatorAvInfo())) {
                logger.info("purolator_av_info field value is one of following GeneralDelivery, POBox, RuralRoute");
                if(hasStreetType(pinInfo)) {
                    logger.info("Street type is present.");
                    logger.info(String.format("Update pinMaster with pinMasterStateCode FAM."));
                    getPinMasterSvc().updatePinMaster(connection, pinMaster.getPinMasterId(), AutomatchWorkerConstants.PIN_MASTER_STATE_CODE_FAM,sourceWorker);
                    //Get updated PinMaster record
                    PinMaster pinMasterUpdated = getPinMasterSvc().findPinMasterRecordByPiecePin(connection, pinMaster.getPiecePin());
                    publishToPinActivitySQS(pinMasterUpdated, pinInfo, automatchModel, AutomatchWorkerConstants.MESSAGE_FAILURE,
                            "purolator_av_info field value is one of following GeneralDelivery, POBox, RuralRoute",null,null, false,null);
                    publishToAddressTriageSQS(pinMasterUpdated, automatchModel);
                    logger.info("Publishing to PinActivitySQS and AddressTriageSQS for pin: {}", automatchModel.getPiecePin());
                    return;
                } else {
                    logger.info("Street type is not present.");
                    SortPlanCalendar sortPlanCalendar = getSortPlanCalendar(pinInfo.getDestinationTerminal(),connection);
                    if (sortPlanCalendar == null) {
                        logger.info("Sort plan calendar is null for terminal {}.", pinInfo.getDestinationTerminal());
                        //throw an exception to trigger message to go to error queue
                        throw new SmartSortException("Sort plan calendar is null for current terminal. The process is failed", false);
                    }
                    //Update pin_master set pin_master_state_code ='RES', OriginalRoute ='SRR', routeplanversionID = rp.RoutePlanVersionID,
                    logger.info(String.format("Update pinMaster with pinMasterStateCode RES."));
                    getPinMasterSvc().updatePinMasterDetails(connection, pinMaster.getPinMasterId(), AutomatchWorkerConstants.PIN_MASTER_STATE_CODE_RES,
                            AutomatchWorkerConstants.PIN_MASTER_ROUTE_SRR, sortPlanCalendar.getRoutePlanVersionId(), sourceWorker);
                    //Get updated PinMaster record
                    PinMaster pinMasterUpdated = getPinMasterSvc().findPinMasterRecordByPiecePin(connection, pinMaster.getPiecePin());
                    publishToPinActivitySQS(pinMasterUpdated, pinInfo, automatchModel, AutomatchWorkerConstants.MESSAGE_FAILURE,
                            "purolator_av_info field value is one of following GeneralDelivery, POBox, RuralRoute",null,null, false,null);
                    logger.info("Publishing to PinActivitySQS for pin: {}", automatchModel.getPiecePin());
                    return;
                }

            }
        } catch (Exception e) {
            logger.error("Error checking pinInfo purolator_av_info: {}", e.getMessage());
            throw new SmartSortException(e.getMessage(), false);
        }

        try {
            if (AutomatchWorkerConstants.PUROLATOR_AV_STATUS_UNPARSABLE_LIST.contains(pinInfo.getPurolatorAvStatus())) {
                logger.info("purolator_av_status is Unparsable or AWSUnavailible");
                // Create hash from declared address information
                String declaredAddressHash = pinInfoSvc.GetDeclaredHashAddress(pinInfo);
                logger.info("Will lookup Triage History record for declared address hash: " + declaredAddressHash);

                // Lookup triageHistory table for declared_address_hash
                TriageHistory triageHistory = getTriageHistorySvc().findTriageHistoryRecordByAddressHash(declaredAddressHash,connection);

                if (triageHistory == null) {
                    // No record found in triage_history
                    logger.info("No record found in triage_history.");
                    logger.info(String.format("Update pinMaster with pinMasterStateCode FAM."));
                    getPinMasterSvc().updatePinMaster(connection, pinMaster.getPinMasterId(), AutomatchWorkerConstants.PIN_MASTER_STATE_CODE_FAM,sourceWorker);
                    //Get updated PinMaster record
                    PinMaster pinMasterUpdated = getPinMasterSvc().findPinMasterRecordByPiecePin(connection, pinMaster.getPiecePin());
                    publishToPublishEventSQS(pinMasterUpdated,automatchModel, AutomatchWorkerConstants.EVENT_CODE_4009);
                    publishToPinActivitySQS(pinMasterUpdated, pinInfo, automatchModel, AutomatchWorkerConstants.MESSAGE_FAILURE, "No record found in triage_history",null,null, false,null);
                    publishToAddressTriageSQS(pinMasterUpdated, automatchModel);
                    logger.info("Publishing to PinActivitySQS and AddressTriageSQS for pin: {}", automatchModel.getPiecePin());
                    return;
                } else {
                    // Record found in triage_history
                    logger.info(String.format("Record found in triage_history"));

                    LocalDateTime currentDate = LocalDateTime.now();
                    //Set startDateTime and endDateTime as LocalDateTime
                    LocalDateTime startDateTime = LocalDateTime.ofInstant(
                            triageHistory.getStartDate().toInstant(), ZoneId.systemDefault()
                    );
                    LocalDateTime endDateTime = Optional.ofNullable(triageHistory.getEndDate())
                            .map(date -> LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()))
                            .orElse(null);

                    // Check if the current date is within the range of start_date and end_date
                    if (triageHistory.getEndDate() != null && (currentDate.isAfter(startDateTime) && currentDate.isBefore(endDateTime))) {
                        // Use route and shelf from triage_history
                        logger.info("Current date is within the range of start_date and end_date. Using route and shelf from triage_history.");
                        getPinMasterSvc().updatePinMasterDetailsWithTriageAndRouteInfo(
                                connection,
                                pinMaster.getPinMasterId(),
                                triageHistory,
                                null,
                                sourceWorker,
                                AutomatchWorkerConstants.TRIAGE_TYPE_A,
                                AutomatchWorkerConstants.PIN_MASTER_STATE_CODE_RER,
                                triageHistory.getShelf()
                        );

                        // Push messages to SQS
                        PinMaster pinMasterUpdated = getPinMasterSvc().findPinMasterRecordByPiecePin(connection, pinMaster.getPiecePin());
                        publishToPublishEventSQS(pinMasterUpdated, automatchModel, AutomatchWorkerConstants.EVENT_CODE_4000);
                        publishToPinActivitySQS(
                                pinMasterUpdated,
                                pinInfo,
                                automatchModel,
                                AutomatchWorkerConstants.MESSAGE_SUCCESS,
                                "Match found using Triage History route and shelf",
                                null,
                                null,
                                false,
                                null
                        );
                        return;
                    }
                    else {
                        // Current date is outside the range or end_date is null
                        logger.info("Current date is outside the range of start_date and end_date or end_date is null. Using correct address information from triage_history.");
                        RouteInformation routeInfo = getRouteInformationSvc().findRouteInformation(connection, pinInfo.getDestinationTerminal(),
                                triageHistory.getCorrectPostalCode(), triageHistory.getCorrectStreetName(), triageHistory.getCorrectStreetType(),
                                triageHistory.getCorrectStreetDir(), triageHistory.getCorrectStreetNum(), triageHistory.getCorrectStreetNumSuf(),
                                pinMaster.getUnitNumber(), triageHistory.getCorrectCity(), pinMaster.getCustomerName(),
                                pinInfo.getHoldForPickup(), pinMaster.getServiceTime(), pinInfo.getPurolatorAvStatus(), false);

                        if (routeInfo == null || (!routeInfo.getRouteFound() || routeInfo.getShelf() == null || routeInfo.getRoute() == null)) {
                            // No match found
                            logger.info("No match found in GetRouteInformation.");
                            logger.info(String.format("Update pinMaster with pinMasterStateCode FAM."));
                            getPinMasterSvc().updatePinMaster(connection, pinMaster.getPinMasterId(), AutomatchWorkerConstants.PIN_MASTER_STATE_CODE_FAM, sourceWorker);
                            PinMaster pinMasterUpdated = getPinMasterSvc().findPinMasterRecordByPiecePin(connection, pinMaster.getPiecePin());
                            publishToPublishEventSQS(pinMasterUpdated, automatchModel, AutomatchWorkerConstants.EVENT_CODE_4009);
                            publishToPinActivitySQS(pinMasterUpdated, pinInfo, automatchModel, AutomatchWorkerConstants.MESSAGE_FAILURE, AutomatchWorkerConstants.MESSAGE_GET_RP_FAILURE,
                                    null, null, false, null);
                            publishToAddressTriageSQS(pinMasterUpdated, automatchModel);
                            return;
                        } else {
                            // Match found
                            logger.info("Match found in GetRouteInformation using Triage History correct address.");
                            logger.info(String.format("Update pinMaster with pinMasterStateCode RER."));
                            getPinMasterSvc().updatePinMasterDetailsWithTriageAndRouteInfo(connection, pinMaster.getPinMasterId(), triageHistory, routeInfo, sourceWorker,
                                    AutomatchWorkerConstants.TRIAGE_TYPE_A, AutomatchWorkerConstants.PIN_MASTER_STATE_CODE_RER, routeInfo.getShelf());
                            PinMaster pinMasterUpdated = getPinMasterSvc().findPinMasterRecordByPiecePin(connection, pinMaster.getPiecePin());
                            publishToPublishEventSQS(pinMasterUpdated, automatchModel, AutomatchWorkerConstants.EVENT_CODE_4000);
                            publishToPinActivitySQS(pinMasterUpdated, pinInfo, automatchModel, AutomatchWorkerConstants.MESSAGE_SUCCESS, "Match found in route plan using Triage History",
                                    null, routeInfo, false, null);
                            return;
                        }
                    }
                }
            } else {
                // purolator_av_status is not 'Unparsable' or 'AWSUnavailible'
                logger.info("purolator_av_status is not 'Unparsable' or 'AWSUnavailible'");
                // Call GetRouteInformation SP using current address information
                logger.info(String.format("Use pin info current address info to call GetRouteInformation SP"));
                RouteInformation routeInfoWithPinInfoParams = getRouteInformationSvc().findRouteInformation(connection, pinInfo.getDestinationTerminal(),
                        pinInfo.getCurrentPostalCode(), pinInfo.getCurrentStreetName(), pinInfo.getCurrentStreetType(),
                        pinInfo.getCurrentStreetDirection(), pinInfo.getCurrentStreetNumber(), pinInfo.getCurrentStreetNumberSuffix(),
                        pinMaster.getUnitNumber(), pinInfo.getCurrentCity(), pinMaster.getCustomerName(),
                        pinInfo.getHoldForPickup(), pinMaster.getServiceTime(), pinInfo.getPurolatorAvStatus(), false);
                // Get shelf override
                shelfOverride = determineShelfOverride(pinMaster,routeInfoWithPinInfoParams,connection);

                if (routeInfoWithPinInfoParams != null && routeInfoWithPinInfoParams.getRouteFound() && routeInfoWithPinInfoParams.getShelf() != null && routeInfoWithPinInfoParams.getRoute() != null)  {
                    // Match found in GetRouteInformation
                    logger.info(String.format("Match found in GetRouteInformation using pin info current address."));
                    logger.info("The content of GetRouteInformation returned: {}", objectMapper.writeValueAsString(routeInfoWithPinInfoParams));

                    logger.info(String.format("Update pinMaster with pinMasterStateCode RER."));
                    getPinMasterSvc().updatePinMasterDetailsWithPinInfoAndRouteInformation(connection, pinMaster.getPinMasterId(),pinInfo, routeInfoWithPinInfoParams, sourceWorker,
                            AutomatchWorkerConstants.PIN_MASTER_STATE_CODE_RER, shelfOverride);
                    //Get updated PinMaster record
                    PinMaster pinMasterUpdated = getPinMasterSvc().findPinMasterRecordByPiecePin(connection, pinMaster.getPiecePin());
                    publishToPublishEventSQS(pinMasterUpdated,automatchModel, AutomatchWorkerConstants.EVENT_CODE_4000);
                    publishToPinActivitySQS(pinMasterUpdated, pinInfo, automatchModel, AutomatchWorkerConstants.MESSAGE_SUCCESS, "Match found with route info call",
                            null,routeInfoWithPinInfoParams, false,null);
                    return;
                } else {
//                    // No match found in route plan, lookup using declared address hash
                    logger.info(String.format("No match found in route plan, lookup using declared address hash."));
                        String currentAddressHash = pinInfoSvc.GetCurrentHashAddress(pinInfo);
                        String declaredAddressHash = pinInfoSvc.GetDeclaredHashAddress(pinInfo);
                        // Lookup triageHistory table for declared_address_hash
                        TriageHistory triageHistory = getTriageHistorySvc().findTriageHistoryRecordByAddressHash(declaredAddressHash,connection);

                        if(triageHistory == null) {
                            // No record found in triage_history by declared hash
                            logger.info(String.format("No record found in triage_history by declared hash"));
                            triageHistory = getTriageHistorySvc().findTriageHistoryRecordByAddressHash(currentAddressHash,connection);
                        }

                        if(triageHistory == null){
                            // No record found with both declared and current address hash
                            logger.info(String.format("No record found with both declared and current address hash"));
                            logger.info(String.format("Update pinMaster with pinMasterStateCode FAM."));
                            getPinMasterSvc().updatePinMaster(connection, pinMaster.getPinMasterId(), AutomatchWorkerConstants.PIN_MASTER_STATE_CODE_FAM,sourceWorker);
                            //Get updated PinMaster record
                            PinMaster pinMasterUpdated = getPinMasterSvc().findPinMasterRecordByPiecePin(connection, pinMaster.getPiecePin());
                            publishToPublishEventSQS(pinMasterUpdated,automatchModel, AutomatchWorkerConstants.EVENT_CODE_4009);
                            publishToPinActivitySQS(pinMasterUpdated, pinInfo, automatchModel, AutomatchWorkerConstants.MESSAGE_FAILURE, AutomatchWorkerConstants.MESSAGE_GET_RP_FAILURE,
                                    null,null, false,null);
                            publishToAddressTriageSQS(pinMasterUpdated, automatchModel);
                            return;
                        }
                        else {
                            // Record found in triage_history
                            logger.info(String.format("Record found in triage_history"));

                            LocalDateTime currentDate = LocalDateTime.now();
                            //Set startDateTime and endDateTime as LocalDateTime
                            LocalDateTime startDateTime = LocalDateTime.ofInstant(
                                    triageHistory.getStartDate().toInstant(), ZoneId.systemDefault()
                            );
                            LocalDateTime endDateTime = Optional.ofNullable(triageHistory.getEndDate())
                                    .map(date -> LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()))
                                    .orElse(null);

                            // Check if the current date is within the range of start_date and end_date
                            if (triageHistory.getEndDate() != null && (currentDate.isAfter(startDateTime) && currentDate.isBefore(endDateTime))) {
                                // Use route and shelf from triage_history
                                logger.info("Current date is within the range of start_date and end_date. Using route and shelf from triage_history.");
                                getPinMasterSvc().updatePinMasterDetailsWithTriageAndRouteInfo(
                                        connection,
                                        pinMaster.getPinMasterId(),
                                        triageHistory,
                                        null,
                                        sourceWorker,
                                        AutomatchWorkerConstants.TRIAGE_TYPE_A,
                                        AutomatchWorkerConstants.PIN_MASTER_STATE_CODE_RER,
                                        triageHistory.getShelf()
                                );

                                // Push messages to SQS
                                PinMaster pinMasterUpdated = getPinMasterSvc().findPinMasterRecordByPiecePin(connection, pinMaster.getPiecePin());
                                publishToPublishEventSQS(pinMasterUpdated, automatchModel, AutomatchWorkerConstants.EVENT_CODE_4000);
                                publishToPinActivitySQS(
                                        pinMasterUpdated,
                                        pinInfo,
                                        automatchModel,
                                        AutomatchWorkerConstants.MESSAGE_SUCCESS,
                                        "Match found using Triage History route and shelf",
                                        null,
                                        null,
                                        false,
                                        null
                                );
                                return;
                            }
                            else {
                                // Current date is outside the range or end_date is null
                                logger.info("Current date is outside the range of start_date and end_date or end_date is null. Using correct address information from triage_history.");
                                RouteInformation routeInfoWithTriageHistoryParams = getRouteInformationSvc().findRouteInformation(connection, pinInfo.getDestinationTerminal(),
                                        triageHistory.getCorrectPostalCode(), triageHistory.getCorrectStreetName(), triageHistory.getCorrectStreetType(),
                                        triageHistory.getCorrectStreetDir(), triageHistory.getCorrectStreetNum(), triageHistory.getCorrectStreetNumSuf(),
                                        pinMaster.getUnitNumber(), triageHistory.getCorrectCity(), pinMaster.getCustomerName(),
                                        pinInfo.getHoldForPickup(), pinMaster.getServiceTime(), pinInfo.getPurolatorAvStatus(), false);

                                if (routeInfoWithTriageHistoryParams == null) {
                                    // No match found
                                    logger.info("No match found in GetRouteInformation.");
                                    logger.info(String.format("Update pinMaster with pinMasterStateCode FAM."));
                                    getPinMasterSvc().updatePinMaster(connection, pinMaster.getPinMasterId(), AutomatchWorkerConstants.PIN_MASTER_STATE_CODE_FAM, sourceWorker);
                                    PinMaster pinMasterUpdated = getPinMasterSvc().findPinMasterRecordByPiecePin(connection, pinMaster.getPiecePin());
                                    publishToPublishEventSQS(pinMasterUpdated, automatchModel, AutomatchWorkerConstants.EVENT_CODE_4009);
                                    publishToPinActivitySQS(pinMasterUpdated, pinInfo, automatchModel, AutomatchWorkerConstants.MESSAGE_FAILURE, AutomatchWorkerConstants.MESSAGE_GET_RP_FAILURE,
                                            null, null, false, null);
                                    publishToAddressTriageSQS(pinMasterUpdated, automatchModel);
                                    return;
                                } else {
                                    // Match found
                                    logger.info("Match found in GetRouteInformation using Triage History correct address.");
                                    logger.info(String.format("Update pinMaster with pinMasterStateCode RER."));
                                    getPinMasterSvc().updatePinMasterDetailsWithTriageAndRouteInfo(connection, pinMaster.getPinMasterId(), triageHistory, routeInfoWithTriageHistoryParams, sourceWorker,
                                            AutomatchWorkerConstants.TRIAGE_TYPE_A, AutomatchWorkerConstants.PIN_MASTER_STATE_CODE_RER, routeInfoWithTriageHistoryParams.getShelf());
                                    PinMaster pinMasterUpdated = getPinMasterSvc().findPinMasterRecordByPiecePin(connection, pinMaster.getPiecePin());
                                    publishToPublishEventSQS(pinMasterUpdated, automatchModel, AutomatchWorkerConstants.EVENT_CODE_4000);
                                    publishToPinActivitySQS(pinMasterUpdated, pinInfo, automatchModel, AutomatchWorkerConstants.MESSAGE_SUCCESS, "Match found in route plan using Triage History",
                                            null, routeInfoWithTriageHistoryParams, false, null);
                                    return;
                                }
                            }
                        }
                }
            }
        } catch (Exception e) {
            logger.error( AutomatchWorkerConstants.ERROR_PROCESSING_ROUTE_PLAN_LOOKUP, e);
            throw new SmartSortException(e.getMessage(), false);
        }
    }

    @Override
    protected AutomatchWorkerContext getWorkerContext() {
        return workerContext;
    }

    private Map<String, SQSEvent.MessageAttribute> getMsgAttributes(ProcessingObject processingObject) {
        if (msgAttr == null) {
            //msgAttr = new HashMap<>();
            msgAttr = processingObject.getSqsMsg().getMessageAttributes();
        }
        return msgAttr;
    }

    private PinMasterSvc getPinMasterSvc() {
        if (pinMasterSvc == null) {
            pinMasterSvc = new PinMasterSvcImpl(workerContext);
        }
        return pinMasterSvc;
    }

    private TriageQueueSvc getTriageQueueSvc() {
        if (triageQueueSvc == null) {
            triageQueueSvc = new TriageQueueSvcImpl(workerContext);
        }
        return triageQueueSvc;
    }

    private TriageHistorySvc getTriageHistorySvc() {
        if (triageHistorySvc == null) {
            triageHistorySvc = new TriageHistorySvcImpl(workerContext);
        }
        return triageHistorySvc;
    }

    private FacilityMasterSvc getFacilityMasterSvc() {
        if (facilityMasterSvc == null) {
            facilityMasterSvc = new FacilityMasterSvcImpl(workerContext);
        }
        return facilityMasterSvc;
    }

    private SortPlanCalendarSvc getSortPlanCalendarSvc() {
        if (sortPlanCalendarSvc == null) {
            sortPlanCalendarSvc = new SortPlanCalendarSvcImpl(workerContext);
        }
        return sortPlanCalendarSvc;
    }

    private SupSystemParametersSvc getSupSystemParametersSvc() {
        if (supSystemParametersSvc == null) {
            supSystemParametersSvc = new SupSystemParametersSvcImpl(workerContext);
        }
        return supSystemParametersSvc;
    }

    private PinInfoSvc getPinInfoSvc() {
        if (pinInfoSvc == null) {
            pinInfoSvc = new PinInfoSvcImpl(workerContext);
        }
        return pinInfoSvc;
    }

    private RouteInformationSvc getRouteInformationSvc() {
        if (routeInformationSvc == null) {
            routeInformationSvc = new RouteInformationSvcImpl(workerContext);
        }
        return routeInformationSvc;
    }

    private boolean hasStreetType(PinInfo pinInfo) {
        try {
            // Predefined list of street types
            List<String> streetTypes = Arrays.asList("RD", "ST", "AVE", "BLVD", "DR", "LN", "CT", "PL", "TER", "WAY");

            // Check if declared_address_line1 is not null or empty
            if (pinInfo.getDeclaredAddressLine1() == null || pinInfo.getDeclaredAddressLine1().isEmpty()) {
                return false;
            }

            // Split the address into words and check if any word matches a street type
            String[] addressParts = pinInfo.getDeclaredAddressLine1().toUpperCase().split("\\s+");
            for (String part : addressParts) {
                if (streetTypes.contains(part)) {
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            // Log the error and return false
            logger.error("Error checking street type in declared_address_line1: {}", e.getMessage(), e);
           return false;
        }
    }

    private String getShelfOverride(String paramName, String paramContext, String paramContextValue,Connection connection) throws SmartSortException {

        try {
            // Use the getParamValue method to get the shelf override value
            return getSupSystemParametersSvc().getParamValue(paramName, paramContext, paramContextValue, connection);
        } catch (SmartSortException e) {
            // Log the error and throw a SmartSortException
            logger.error(AutomatchWorkerConstants.ERROR_RETRIEVING_SHELF_OVERRIDE + " for paramName: {}, paramContext: {}, paramContextValue: {}", paramName, paramContext, paramContextValue, e);
            throw new SmartSortException(AutomatchWorkerConstants.ERROR_RETRIEVING_SHELF_OVERRIDE, e, e.isRecoverable());
        }
    }

    /**
     * Retrieves the sort calendar for a given terminal and working date.
     *
     * The logic determines the working date based on the current time and whether
     * the initial start time was before noon and the current time is after noon.
     * If the condition is met, the working date is set to the next day; otherwise,
     * it is set to the current day.
     *
     * @param terminal The terminal for which the sort calendar is to be retrieved.
     * @param connection The database connection to be used for the operation.
     * @throws SmartSortException If an error occurs while retrieving the sort calendar.
     */
    private SortPlanCalendar getSortPlanCalendar(String terminal, Connection connection) throws SmartSortException {
        LocalDateTime currentTime = LocalDateTime.now();


        try {
            // Determine the working date
            String workingDateStr = getSupSystemParametersSvc().getParamValue(AutomatchWorkerConstants.SUP_SYSTEM_PARAM_NAME_WORKING_DATE,
                    AutomatchWorkerConstants.SUP_SYSTEM_PARAM_CONTEXT_TERMINAL, terminal, connection);
            java.sql.Date workingDateDt = DateUtil.convertStringToSqlDate(workingDateStr);


            // Retrieve the sort calendar for the specified terminal and working date
            logger.info("Successfully retrieved sort calendar for terminal: {} and working date: {}", terminal, workingDateDt);
            return getSortPlanCalendarSvc().getSortPlanCalendarByFacility(terminal, workingDateDt, connection);
        } catch (SmartSortException e) {
            logger.error(AutomatchWorkerConstants.ERROR_RETRIEVING_SORT_CALENDAR + " {}. {}", terminal, e.getMessage());
            throw new SmartSortException(AutomatchWorkerConstants.ERROR_RETRIEVING_SORT_CALENDAR, e, true);
        } catch (Exception e) {
            logger.error("Unexpected error while retrieving sort calendar: {}", e.getMessage(), e);
            throw new SmartSortException("Unexpected error occurred.", e, false);
        }
    }
    private void publishToPinActivitySQS(PinMaster pinMaster, PinInfo pinInfo, AutomatchModel automatchModel,String status, String message, SIAPIResponse siapiResponse,
                                         RouteInformation routeInformation, boolean isResolvedByApi, String siApiType) throws SmartSortException {
        // Implement the logic to publish to smartsort-intake-dev-pinactivity SQS
        try {
            logger.info("Going to publish PinActivity message for pin: {}", pinMaster.getPiecePin());

            PinActivityEvent pinActivityModel = new PinActivityEvent();
            pinActivityModel.setPiecePin(automatchModel.getPiecePin());
            pinActivityModel.setActivityLogger(sourceWorker);
            pinActivityModel.setPinMasterId(pinMaster.getPinMasterId());
            pinActivityModel.setPinInfoId(pinInfo.getPinInfoId());
            pinActivityModel.setMessageGuid(automatchModel.getMessageGuid());
            pinActivityModel.setPinMasterStateCode(pinMaster.getPinMasterStateCode());
            pinActivityModel.setActivityStatus(status);
            pinActivityModel.setActivityStatusReason(message);
            pinActivityModel.setRoutePlanVersionId(pinMaster.getRoutePlanVersionId());
            pinActivityModel.setAutomatchSource(siApiType);
            if (isResolvedByApi) {
                pinActivityModel.setRoute(siapiResponse.getRoute());
                pinActivityModel.setShelf(siapiResponse.getShelf());
                //pinActivityModel.setMatchType("API Type");
            } else {
                pinActivityModel.setRoute(pinMaster.getOriginalRoute());
                pinActivityModel.setShelf(pinMaster.getOriginalShelf());
                //pinActivityModel.setMatchType("AutoMatch");
            }

            if(status.equalsIgnoreCase(AutomatchWorkerConstants.MESSAGE_SUCCESS)) {
                if (isResolvedByApi) {
                    pinActivityModel.setMatchType("API Type");
                } else {
                    pinActivityModel.setMatchType("AutoMatch");
                }
            }

            if(status.equalsIgnoreCase(AutomatchWorkerConstants.MESSAGE_FAILURE)) {
                pinActivityModel.setNomatchReason(AutomatchWorkerConstants.MATCH_TYPE_NO_MATCH);
            }
//            else {
//                pinActivityModel.setNomatchReason(null);
//            }
            pinActivityModel.setMessageFrom(automatchModel.getPublishedBy());
            pinActivityModel.setMessageDatetime(automatchModel.getEventDateTime());
            pinActivityModel.setActivityCompleteDatetime(DateUtil.formatToJsonDateUTC(LocalDateTime.now()));

            String pinActivityJson = objectMapper.writeValueAsString(pinActivityModel);
            if(!sendSqsMessage(PIN_ACTIVITY_QUEUE, pinActivityJson, 0)){
                logger.error(AutomatchWorkerConstants.ERROR_PUBLISHING_PIN_ACTIVITY + ": {}. Will retry.");
                throw new SmartSortException(AutomatchWorkerConstants.ERROR_PUBLISHING_PIN_ACTIVITY + ": {}. Will retry.", true);
            };

            logger.info("Published PinActivity to SQS: {}", pinActivityJson);
        } catch (Exception e) {
            logger.error(AutomatchWorkerConstants.ERROR_PUBLISHING_PIN_ACTIVITY + ": {}. Will retry.", e.getMessage(), e);
            throw new SmartSortException(e.getMessage(), e, true);
        }
    }

    private void publishToAddressTriageSQS(PinMaster pinMaster, AutomatchModel automatchModel) throws SmartSortException{
        // Implement the logic to publish to smartsort-intake-stg-Automatch SQS
        try {
            logger.info("Going to publish Address Triage message for pin: {}", automatchModel.getPiecePin());
            AddressTriageEvent addressTriageEvent = new AddressTriageEvent();

            addressTriageEvent.setMessageGUID(automatchModel.getMessageGuid());
            addressTriageEvent.setPiecePin(automatchModel.getPiecePin());
            addressTriageEvent.setPinMasterID(automatchModel.getPinMasterId().toString());
            addressTriageEvent.setPinMasterStateCode(pinMaster.getPinMasterStateCode());
            addressTriageEvent.setEventDateTime(automatchModel.getEventDateTime());
            addressTriageEvent.setPublishedBy(sourceWorker);
            addressTriageEvent.setPublishedDateTime(DateUtil.formatToJsonDateUTC(LocalDateTime.now()));

            String addressTriageMessage = objectMapper.writeValueAsString(addressTriageEvent);
            logger.info(addressTriageMessage);
            if(!sendSqsMessage(ADDRESS_TRIAGE_QUEUE, addressTriageMessage, 0)){
                logger.error(AutomatchWorkerConstants.ERROR_PUBLISHING_ADDRESS_TRIAGE + ": {}. Will retry.");
                throw new SmartSortException(AutomatchWorkerConstants.ERROR_PUBLISHING_ADDRESS_TRIAGE + ": {}. Will retry.", true);
            };

            logger.info("Published Address Triage to SQS: {}", addressTriageMessage);
        } catch (Exception e) {
            logger.error("Error publishing PinActivity to SQS: {}. Will retry.", e.getMessage(), e);
            throw new SmartSortException(e.getMessage(), e, true);
        }
    }

    private void publishToPublishEventSQS(PinMaster pinMaster, AutomatchModel automatchModel, String eventCode) throws SmartSortException{
        // Implement the logic to publish to smartsort-intake-stg-Automatch SQS
        try {
            logger.info("Going to push Publish Event message for pin: {}, event code: {}", automatchModel.getPiecePin(), eventCode);
            EventToPublish eventToPublish = new EventToPublish();

            eventToPublish.setMessageGUID(automatchModel.getMessageGuid());
            eventToPublish.setPublishedDateTime(DateUtil.formatToJsonDateUTC(LocalDateTime.now()));
            eventToPublish.setTerminalId(pinMaster.getDestinationTerminal());
            eventToPublish.setSourceSystemId(AutomatchWorkerConstants.SOURCE_SYSTEM_ID);
            eventToPublish.setSourceSystemVersion(AutomatchWorkerConstants.SOURCE_SYSTEM_ID_VERSION);
            eventToPublish.setSubProcessId(sourceWorker);
            eventToPublish.setRoute(pinMaster.getOriginalRoute());
            eventToPublish.setEventTimeStamp(automatchModel.getEventDateTime());
            eventToPublish.setEventTypeCode(eventCode);

            eventToPublish.setPiecePin(automatchModel.getPiecePin());
            eventToPublish.setPinMasterId(pinMaster.getPinMasterId());
            eventToPublish.setPinInfoId(pinMaster.getPinInfoId());

            if(eventCode.equalsIgnoreCase(AutomatchWorkerConstants.EVENT_CODE_4000)){
                eventToPublish.setDeliverySeqId(pinMaster.getDeliverySeqId().toString());
                eventToPublish.setShelf(pinMaster.getOriginalShelf());
                eventToPublish.setTruckShelfOverride(pinMaster.getOriginalTruckShelfOverride());
                eventToPublish.setAlternateAddressFlag(pinMaster.getAlternateAddressFlag());
                eventToPublish.setLongitude(pinMaster.getLongitude().toString());
                eventToPublish.setLatitude(pinMaster.getLatitude().toString());
            }

            String eventToPublishMessage = objectMapper.writeValueAsString(eventToPublish);
            logger.info(eventToPublishMessage);
            if(!sendSqsMessage(PUBLISH_EVENT_QUEUE, eventToPublishMessage, 0)){
                logger.error(AutomatchWorkerConstants.ERROR_PUSHING_PUBLISH_EVENT + ": {}. Will retry.");
                throw new SmartSortException(AutomatchWorkerConstants.ERROR_PUSHING_PUBLISH_EVENT + ": {}. Will retry.", true);
            };

            logger.info("Pushed Publish Event to SQS: {}", eventToPublishMessage);
        } catch (Exception e) {
            logger.error("Error pushing Publish Event to SQS: {}. Will retry.", e.getMessage(), e);
            throw new SmartSortException(e.getMessage(), e, true);
        }
    }

    private void publishToPublishEventSQSWithSIAPIResponse(PinMaster pinMaster, AutomatchModel automatchModel, String eventCode, String shelfOveride, SIAPIResponse siapiResponse) throws SmartSortException{
        // Implement the logic to publish to smartsort-intake-stg-Automatch SQS
        try {
            logger.info("Going to push Publish Event message for pin: {}, event code: {}", automatchModel.getPiecePin(), eventCode);
            EventToPublish eventToPublish = new EventToPublish();

            eventToPublish.setMessageGUID(automatchModel.getMessageGuid());
            eventToPublish.setPublishedDateTime(DateUtil.formatToJsonDateUTC(LocalDateTime.now()));
            eventToPublish.setTerminalId(pinMaster.getDestinationTerminal());
            eventToPublish.setSourceSystemId(AutomatchWorkerConstants.SOURCE_SYSTEM_ID);
            eventToPublish.setSourceSystemVersion(AutomatchWorkerConstants.SOURCE_SYSTEM_ID_VERSION);
            eventToPublish.setSubProcessId(sourceWorker);
            eventToPublish.setRoute(siapiResponse.getRoute());
            eventToPublish.setEventTimeStamp(automatchModel.getEventDateTime());
            eventToPublish.setEventTypeCode(eventCode);

            eventToPublish.setPiecePin(automatchModel.getPiecePin());
            eventToPublish.setPinMasterId(pinMaster.getPinMasterId());
            eventToPublish.setPinInfoId(pinMaster.getPinInfoId());

            if(eventCode.equalsIgnoreCase(AutomatchWorkerConstants.EVENT_CODE_4000)){
                eventToPublish.setDeliverySeqId(pinMaster.getDeliverySeqId().toString());
                eventToPublish.setShelf(siapiResponse.getShelf());
                eventToPublish.setTruckShelfOverride(shelfOveride);
                eventToPublish.setAlternateAddressFlag(pinMaster.getAlternateAddressFlag());
                eventToPublish.setLongitude(pinMaster.getLongitude().toString());
                eventToPublish.setLatitude(pinMaster.getLatitude().toString());
            }

            String eventToPublishMessage = objectMapper.writeValueAsString(eventToPublish);
            logger.info(eventToPublishMessage);
            if(!sendSqsMessage(PUBLISH_EVENT_QUEUE, eventToPublishMessage, 0)){
                logger.error(AutomatchWorkerConstants.ERROR_PUSHING_PUBLISH_EVENT + ": {}. Will retry.");
                throw new SmartSortException(AutomatchWorkerConstants.ERROR_PUSHING_PUBLISH_EVENT + ": {}. Will retry.", true);
            };

            logger.info("Pushed Publish Event to SQS: {}", eventToPublishMessage);
        } catch (Exception e) {
            logger.error("Error pushing Publish Event to SQS: {}. Will retry.", e.getMessage(), e);
            throw new SmartSortException(e.getMessage(), e, true);
        }
    }


    /**
     * Determines the appropriate shelf override value based on precedence rules.
     *
     * Precedence rules:
     * 1. If `prShelfOverride` is not null or empty, it is used.
     * 2. If `prShelfOverride` is null or empty, `dgShelfOverride` is used.
     * 3. If both `prShelfOverride` and `dgShelfOverride` are null or empty,
     *    `originalTruckShelfOverride` from the PinMaster record is used.
     * 4. If all values are null or empty, the default value returned is null.
     *
     * @param pinMaster The `PinMaster` object containing details such as dangerous goods class,
     *                  service time, and original truck shelf override.
     * @param routeInformation The `RouteInformation` object .
     * @return The determined shelf override value based on the precedence rules, or null if all are empty.
     */
    private String determineShelfOverride( PinMaster pinMaster, RouteInformation routeInformation, Connection connection) {

        // Apply PR and DG shelf override rules
        String dgShelfOverride=StringUtils.EMPTY;
        String prShelfOverride=StringUtils.EMPTY;
        try {
            dgShelfOverride = getShelfOverride(AutomatchWorkerConstants.SUP_SYSTEM_PARAM_NAME_TRUCK_SHELF_OVERRIDE,
                    AutomatchWorkerConstants.SUP_SYSTEM_PARAMCONTEXT_DG_CLASS,pinMaster.getDangerousGoodsClass(),connection);
        }
        catch (SmartSortException e) {
            logger.info("Error getting DG shelf override. Will default to empty string.");
        }


        try {
            prShelfOverride = getShelfOverride(AutomatchWorkerConstants.SUP_SYSTEM_PARAM_NAME_TRUCK_SHELF_OVERRIDE,
                    AutomatchWorkerConstants.SUP_SYSTEM_PARAM_CONTEXT_SERVICE_TIME,pinMaster.getServiceTime(),connection);
        }
        catch (SmartSortException e) {
            logger.info("Error getting PR shelf override. Will default to empty string.");
        }

        // Check precedence rules and return the appropriate value
        if (StringUtils.isNotEmpty(prShelfOverride)) {
            return prShelfOverride;
        } else if (StringUtils.isNotEmpty(dgShelfOverride)) {
            return dgShelfOverride;
        } else if (routeInformation != null && StringUtils.isNotEmpty(routeInformation.getTruckShelfOverride())) {
           return routeInformation.getTruckShelfOverride();
        }
        else {
            return null; // Default to null if all values are empty
        }
    }
}
