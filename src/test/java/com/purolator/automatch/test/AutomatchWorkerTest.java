package com.purolator.automatch.test;

import com.amazonaws.services.lambda.runtime.events.SQSEvent;
import com.purolator.smartsort.automatch.worker.AutomatchWorker;
import com.purolator.smartsort.common.SmartSortException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.contrib.java.lang.system.EnvironmentVariables;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;

public class AutomatchWorkerTest {

    AutomatchWorker automatchWorker;
    SQSEvent sqsEvent = new SQSEvent();
    AutomatchWorkerTestContext addressTriageWorkerTestContext;
    @Rule
    public final EnvironmentVariables environmentVariables = new EnvironmentVariables();

    @Before
    public void init() throws SmartSortException {
        automatchWorker = new AutomatchWorker();
        addressTriageWorkerTestContext = new AutomatchWorkerTestContext();

        try
        {
            InputStream input = getClass().getResourceAsStream("/application.properties");
            Properties props = new Properties();
            props.load(input);
            input.close();

            for (String key : props.stringPropertyNames())
            {
                environmentVariables.set(key, props.getProperty(key));
            }
        }
        catch (Exception ex)
        {
            throw new RuntimeException(ex);
        }

        addressTriageWorkerTestContext.getDataSource();
        environmentVariables.set("Environment", "local");
    }

    @Test
    public void testAddressTriage() {
        BufferedReader br = new BufferedReader(new InputStreamReader(this.getClass().getResourceAsStream("/input/" + "automatch_single.json"), Charset.forName("UTF-8")));
        String json = (String)br.lines().collect(Collectors.joining(System.lineSeparator()));

        SQSEvent.SQSMessage sqsMessage = new SQSEvent.SQSMessage();
        sqsMessage.setBody(json);
        sqsMessage.setMessageAttributes(new HashMap<>());
        List<SQSEvent.SQSMessage> records = new ArrayList<>();
        records.add(sqsMessage);
        sqsEvent.setRecords(records);
        String response = automatchWorker.handleRequest(sqsEvent, addressTriageWorkerTestContext);

        Assert.assertNull(response);
    }
}
