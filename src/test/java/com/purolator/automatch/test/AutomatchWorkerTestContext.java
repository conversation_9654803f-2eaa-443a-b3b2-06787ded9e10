package com.purolator.automatch.test;

import com.amazonaws.services.lambda.runtime.ClientContext;
import com.amazonaws.services.lambda.runtime.CognitoIdentity;
import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.LambdaLogger;
import com.purolator.smartsort.common.SmartSortException;
import org.apache.commons.dbcp2.BasicDataSource;

import javax.sql.DataSource;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

public class AutomatchWorkerTestContext implements Context {
    private volatile DataSource dataSource = null;

    private String awsRequestId = "EXAMPLE-REQ-ID";
    private ClientContext clientContext;
    private String functionName = "EXAMPLE";
    private CognitoIdentity identity;
    private String logGroupName = "EXAMPLE";
    private String logStreamName = "EXAMPLE";
    private LambdaLogger logger = new TestLogger();
    private int memoryLimitInMB = 128;
    private int remainingTimeInMillis = 15000;
    private String functionVersion = "EXAMPLE";
    private String invokedFunctionArn = "EXAMPLE";

    @Override
    public String getAwsRequestId() {
        return awsRequestId;
    }

    public void setAwsRequestId(String value) {
        awsRequestId = value;
    }

    @Override
    public ClientContext getClientContext() {
        return clientContext;
    }

    public void setClientContext(ClientContext value) {
        clientContext = value;
    }

    @Override
    public String getFunctionName() {
        return functionName;
    }

    public void setFunctionName(String value) {
        functionName = value;
    }

    @Override
    public CognitoIdentity getIdentity() {
        return identity;
    }

    public void setIdentity(CognitoIdentity value) {
        identity = value;
    }

    @Override
    public String getLogGroupName() {
        return logGroupName;
    }

    public void setLogGroupName(String value) {
        logGroupName = value;
    }

    @Override
    public String getLogStreamName() {
        return logStreamName;
    }

    public void setLogStreamName(String value) {
        logStreamName = value;
    }
    //            workerLogger.info(String.format("%s", sqsMessage));
    @Override
    public LambdaLogger getLogger() {
        return logger;
    }

    public void setLogger(LambdaLogger value) {
        logger = value;
    }

    @Override
    public int getMemoryLimitInMB() {
        return memoryLimitInMB;
    }

    public void setMemoryLimitInMB(int value) {
        memoryLimitInMB = value;
    }

    @Override
    public int getRemainingTimeInMillis() {
        return remainingTimeInMillis;
    }

    public void setRemainingTimeInMillis(int value) {
        remainingTimeInMillis = value;
    }

    @Override
    public String getFunctionVersion() {
        return functionVersion;
    }

    public void setFunctionVersion(String value) {
        functionVersion = value;
    }

    @Override
    public String getInvokedFunctionArn() {
        return invokedFunctionArn;
    }

    public void setInvokedFunctionArn(String value) {
        invokedFunctionArn = value;
    }

    /**
     * A simple {@code LambdaLogger} that prints everything to stderr.
     */
    private static class TestLogger implements LambdaLogger {

        @Override
        public void log(String message) {
            System.err.println(message);
        }
        @Override
        public void log(byte[] message)
        {

        }
    }

    public DataSource getDataSource() throws SmartSortException {
        if (this.dataSource == null) {
            synchronized(this) {
                if (this.dataSource == null) {
                    this.dataSource = this.createDatasource();
                }
            }
        }
        return this.dataSource;
    }

    protected DataSource createDatasource() throws SmartSortException {
        DBConnectionSettings connectionData = null;
        connectionData = this.getLocalDBSettings(connectionData);

        BasicDataSource basicDataSource = new BasicDataSource();
        basicDataSource.setUsername(connectionData.userName);
        basicDataSource.setPassword(connectionData.password);
        basicDataSource.setDriverClassName("org.postgresql.Driver");
        basicDataSource.setUrl(connectionData.url);
        basicDataSource.setMaxTotal(1);
//        basicDataSource.setDefaultSchema(connectionData.defaultSchema);

        return basicDataSource;
    }

    protected DBConnectionSettings getLocalDBSettings(DBConnectionSettings connectionData) throws SmartSortException {
        try (InputStream input = getClass().getResourceAsStream("/db_local.properties")) {
            if (input != null) {
                Properties props = new Properties();
                props.load(input);
                connectionData = getDBConnectionSettingsFromProps(props);
            }
        } catch (IOException e) {
            throw new SmartSortException("No DB connection settings.", e, false);
        }

        if (connectionData == null) {
            try (InputStream input = new FileInputStream("src/test/resources/db_local.properties")) {
                if (input != null) {
                    Properties props = new Properties();
                    props.load(input);
                    connectionData = getDBConnectionSettingsFromProps(props);
                }
            } catch (IOException e) {
                throw new SmartSortException("No DB connection settings.", e, false);
            }
        }
        return connectionData;
    }

    protected DBConnectionSettings getDBConnectionSettingsFromProps(Properties props) throws SmartSortException {
        DBConnectionSettings dbSettings = new DBConnectionSettings();

        dbSettings.userName = props.getProperty("DB_USER");
        dbSettings.password = props.getProperty("DB_PASSWORD");
        dbSettings.url = props.getProperty("DB_URL");
        dbSettings.engine = props.getProperty("DB_DRIVER");
        dbSettings.poolSize = 1;
        return dbSettings;
    }

    public class DBConnectionSettings {
        public String userName;
        public String password;
        public String engine;
        public String url;
        public int poolSize;
        public String defaultSchema;

        @Override
        public String toString() {
            return "DBConnectionSettings [userName=" + userName + ", engine=" + engine + ", url=" + url + ", poolSize="
                    + poolSize + ", defaultSchema=" + defaultSchema + "]";
        }

    }
}
