<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 https://maven.apache.org/xsd/settings-1.0.0.xsd">
	<servers>
		<server>
			<id>puro-mvn-mvn-repository</id>
			<username>aws</username>
			<password>${env.CODEARTIFACT_AUTH_TOKEN}</password>
		</server>
	</servers>
	<profiles>
		<profile>
			<id>puro-mvn-mvn-repository</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<repositories>
				<repository>
					<id>puro-mvn-mvn-repository</id>
					<url>https://puro-mvn-047528301810.d.codeartifact.us-east-1.amazonaws.com/maven/mvn-repository/</url>
				</repository>
			</repositories>
		</profile>
	</profiles>
	<mirrors>
		<mirror>
			<id>puro-mvn-mvn-repository</id>
			<name>puro-mvn-mvn-repository</name>
			<url>https://puro-mvn-047528301810.d.codeartifact.us-east-1.amazonaws.com/maven/mvn-repository/</url>
			<mirrorOf>puro-mvn-mvn-repository</mirrorOf>
		</mirror>
	</mirrors>
</settings>