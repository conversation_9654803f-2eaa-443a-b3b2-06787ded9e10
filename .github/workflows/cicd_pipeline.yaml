name: 'Build and Deploy'

on:
  push:
    branches:
      - feature/**
      - SMARTS-**
      - develop
      - release/**
      - master
    paths-ignore:
      - '.github/**'

permissions:
  id-token: write
  contents: read

# ------------------------------------------------
# Define Top-level Variables (Workflow-wide)
# ------------------------------------------------
env:
  APPLICATION_NAME: smartsort
  SERVICE_NAME: automatch-worker
  AWS_REGION: "us-east-1"
  CODEARTIFACT_DOMAIN: "puro-mvn"
  CODEARTIFACT_DOMAIN_OWNER: "************"
  JAVA_VERSION: "17"
  MAVEN_VERSION: "3.8.8"
  MAVEN_SKIP_TESTS: "true"
  SONAR_PROJECT_KEY: "puro-org_ops-smartsort-automatch-worker"
  SONAR_ORG: "puro-org"
  SONAR_HOST_URL: "https://sonarcloud.io"
  SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

jobs:
  setup-pipeline-variables:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.set-env.outputs.environment }}
      aws_account: ${{ steps.set-env.outputs.aws_account }}
      lambda_function: ${{ steps.set-env.outputs.lambda_function }}
      target_branch: ${{ steps.set-env.outputs.target_branch }}
      s3_bucket: ${{ steps.set-env.outputs.s3_bucket }}
    steps:
      - name: Set environment variables based on branch
        id: set-env
        run: |
          TARGET_BRANCH="${GITHUB_REF#refs/heads/}"
          echo "target_branch=$TARGET_BRANCH" >> $GITHUB_ENV
          echo "target_branch=$TARGET_BRANCH" >> $GITHUB_OUTPUT

          case "$TARGET_BRANCH" in
            feature/*|SMARTS-*)
              ENVIRONMENT="dev"
              AWS_ACCOUNT="************"
              S3_BUCKET_NAME="smartsort-dev-deployment-bucket"
              ;;
            develop)
              ENVIRONMENT="dev"
              AWS_ACCOUNT="************"
              S3_BUCKET_NAME="smartsort-dev-deployment-bucket"
              ;;
            release/*)
              ENVIRONMENT="stg"
              AWS_ACCOUNT="************"
              S3_BUCKET_NAME="smartsort-stg-deployment-bucket"
              ;;
            master)
              ENVIRONMENT="prod"
              AWS_ACCOUNT="************"
              S3_BUCKET_NAME="smartsort-prod-deployment-bucket"
              ;;
            *)
              echo "Error: Unsupported branch name." >&2
              exit 1
              ;;
          esac

          LAMBDA_FUNCTION="${APPLICATION_NAME}-${ENVIRONMENT}-${SERVICE_NAME}"

          echo "environment=$ENVIRONMENT" >> $GITHUB_ENV
          echo "aws_account=$AWS_ACCOUNT" >> $GITHUB_ENV
          echo "lambda_function=$LAMBDA_FUNCTION" >> $GITHUB_ENV
          echo "s3_bucket=$S3_BUCKET_NAME" >> $GITHUB_ENV

          echo "environment=$ENVIRONMENT" >> $GITHUB_OUTPUT
          echo "aws_account=$AWS_ACCOUNT" >> $GITHUB_OUTPUT
          echo "lambda_function=$LAMBDA_FUNCTION" >> $GITHUB_OUTPUT
          echo "s3_bucket=$S3_BUCKET_NAME" >> $GITHUB_OUTPUT

  build-test-publish:
    needs: setup-pipeline-variables
    runs-on: ubuntu-latest
    env:
      TARGET_BRANCH: ${{ needs.setup-pipeline-variables.outputs.target_branch }}
      LAMBDA_FUNCTION: ${{ needs.setup-pipeline-variables.outputs.lambda_function }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Java
        uses: actions/setup-java@v3
        with:
          distribution: temurin
          java-version: ${{ env.JAVA_VERSION }}

      - name: Set up Maven
        uses: stCarolas/setup-maven@v4.5
        with:
          maven-version: ${{ env.MAVEN_VERSION }}

      - name: Assume AWS role with OIDC
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: "arn:aws:iam::${{ env.CODEARTIFACT_DOMAIN_OWNER }}:role/github-oidc-role"
          aws-region: ${{ env.AWS_REGION }}

      - name: Get CodeArtifact Token
        run: |
          CODEARTIFACT_AUTH_TOKEN=$(aws codeartifact get-authorization-token \
            --domain ${{ env.CODEARTIFACT_DOMAIN }} \
            --domain-owner ${{ env.CODEARTIFACT_DOMAIN_OWNER }} \
            --region ${{ env.AWS_REGION }} \
            --query authorizationToken \
            --output text)
          echo "CODEARTIFACT_AUTH_TOKEN=${CODEARTIFACT_AUTH_TOKEN}" >> "$GITHUB_ENV"

      - name: Build & Test
        run: mvn -B clean verify --settings settings.xml -Dmaven.test.skip=${{ env.MAVEN_SKIP_TESTS }}

      - name: SonarCloud Analysis
        run: |
          mvn sonar:sonar --settings settings.xml \
            -Dsonar.projectKey=${{ env.SONAR_PROJECT_KEY }} \
            -Dsonar.organization=${{ env.SONAR_ORG }} \
            -Dsonar.host.url=${{ env.SONAR_HOST_URL }} \
            -Dsonar.login=${{ secrets.SONAR_TOKEN }} \
            -Dsonar.coverage.jacoco.xmlReportPaths=target/site/jacoco/jacoco.xml

      - name: Copy Artifact
        run: |
          mkdir -p target/lambda
          JAR_FILE=$(ls target/*.jar | head -n 1)
          if [[ -z "$JAR_FILE" ]]; then
            echo "Error: No JAR found!" >&2
            exit 1
          fi
          echo "Uploading artifact: $JAR_FILE"
          cp "$JAR_FILE" target/lambda/

      - name: Upload Artifact to GitHub
        uses: actions/upload-artifact@v4
        with:
          name: BuildNumber-${{ github.run_number }}
          path: target/lambda/*.jar

  deploy:
    needs: [setup-pipeline-variables, build-test-publish]
    runs-on: ubuntu-latest
    env:
      TARGET_BRANCH: ${{ needs.setup-pipeline-variables.outputs.target_branch }}
      LAMBDA_FUNCTION: ${{ needs.setup-pipeline-variables.outputs.lambda_function }}
      AWS_ACCOUNT: ${{ needs.setup-pipeline-variables.outputs.aws_account }}
      S3_BUCKET_NAME: ${{ needs.setup-pipeline-variables.outputs.s3_bucket }}
    steps:
      - name: Assume AWS role with OIDC
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: "arn:aws:iam::${{ env.AWS_ACCOUNT }}:role/github-oidc-role"
          aws-region: ${{ env.AWS_REGION }}

      - name: Download Artifact from GitHub
        uses: actions/download-artifact@v4
        with:
          name: BuildNumber-${{ github.run_number }}
          path: target/lambda/

      - name: Find JAR
        run: |
          ARTIFACT_NAME=$(find target/lambda -maxdepth 1 -type f -name "*.jar" | head -n 1)
          if [[ -z "$ARTIFACT_NAME" ]]; then
            echo "ERROR: No JAR found in target/lambda!" >&2
            exit 1
          fi
          echo "Found artifact: $ARTIFACT_NAME"
          echo "ARTIFACT_NAME=$ARTIFACT_NAME" >> $GITHUB_ENV

      - name: Upload JAR to S3
        run: |
          S3_KEY="${{ env.TARGET_BRANCH }}/${{ github.run_number }}/$(basename $ARTIFACT_NAME)"
          aws s3 cp "$ARTIFACT_NAME" s3://${{ env.S3_BUCKET_NAME }}/$S3_KEY
          echo "S3_KEY=$S3_KEY" >> $GITHUB_ENV

      - name: Update Lambda Function from S3
        run: |
          aws lambda update-function-code \
            --function-name ${{ env.LAMBDA_FUNCTION }} \
            --s3-bucket ${{ env.S3_BUCKET_NAME }} \
            --s3-key ${{ env.S3_KEY }}

      - name: Wait for Lambda Update to Complete
        run: |
          while true; do
            STATE=$(aws lambda get-function --function-name ${{ env.LAMBDA_FUNCTION }} --query 'Configuration.State' --output text)
            LAST_UPDATE_STATUS=$(aws lambda get-function --function-name ${{ env.LAMBDA_FUNCTION }} --query 'Configuration.LastUpdateStatus' --output text)
            if [ "$STATE" == "Active" ] && [ "$LAST_UPDATE_STATUS" == "Successful" ]; then
              break
            else
              echo "Waiting for Lambda update to complete..."
              sleep 10
            fi
          done

      - name: Publish New Lambda Version
        run: |
          FUNCTION_VERSION=$(aws lambda publish-version --function-name ${{ env.LAMBDA_FUNCTION }} --query Version --output text)
          echo "Published Lambda version: $FUNCTION_VERSION"
          echo "FUNCTION_VERSION=$FUNCTION_VERSION" >> $GITHUB_ENV

      - name: Update or Create Alias 'live'
        run: |
          ALIAS_NAME="live"
          ALIAS_EXISTS=$(aws lambda get-alias --function-name ${{ env.LAMBDA_FUNCTION }} --name $ALIAS_NAME --query 'AliasArn' --output text 2>/dev/null || echo "None")

          if [ "$ALIAS_EXISTS" != "None" ]; then
            echo "Updating existing alias $ALIAS_NAME to version $FUNCTION_VERSION"
            aws lambda update-alias --function-name ${{ env.LAMBDA_FUNCTION }} --name $ALIAS_NAME --function-version $FUNCTION_VERSION
          else
            echo "Creating alias $ALIAS_NAME for version $FUNCTION_VERSION"
            aws lambda create-alias --function-name ${{ env.LAMBDA_FUNCTION }} --name $ALIAS_NAME --function-version $FUNCTION_VERSION
          fi
  

